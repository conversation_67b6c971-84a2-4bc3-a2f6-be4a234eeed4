<!-- components/MarkdownRenderer.vue -->
<script setup>
import { ref, watch, onMounted, nextTick } from 'vue';
import MarkdownIt from 'markdown-it'; // 导入 markdown-it

const props = defineProps({
  content: { type: String, default: '' }, // Markdown内容（props传入）
});

const htmlContent = ref(''); // 存储转换后的HTML
const containerRef = ref(null); // 容器DOM引用（用于MathJax渲染）

// 初始化一个 markdown-it 实例，与 shuxue.vue 保持一致
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
});

// 渲染Markdown并触发MathJax渲染
const renderMarkdown = async () => {
  // --- “隔离-修复-还原”逻辑开始 ---

  // 1. 隔离：将所有数学公式提取到数组中，用占位符替换
  const mathExpressions = [];
  const replaceWithPlaceholder = (match) => {
    mathExpressions.push(match);
    return `@@MATH_PLACEHOLDER_${mathExpressions.length - 1}@@`;
  };

  let processedContent = props.content.replace(/\\\\/g, '\\');

  // 提取标准定界符公式
  processedContent = processedContent.replace(/\$\$([\s\S]+?)\$\$/g, replaceWithPlaceholder);
  processedContent = processedContent.replace(/\\\[([\s\S]+?)\\\]/g, replaceWithPlaceholder);
  processedContent = processedContent.replace(/\\\(([\s\S]+?)\\\)/g, replaceWithPlaceholder);
  processedContent = processedContent.replace(
    /(?<!\w)\$([^$\s][\s\S]*?[^$\s])\$(?!\w)/g,
    replaceWithPlaceholder,
  );

  // 提取并包裹 "裸奔" 的 \boxed 命令
  processedContent = processedContent.replace(/(\\boxed\{(?:[^{}]|\{[^{}]*\})+\})/g, (match) => {
    return replaceWithPlaceholder(`$$${match}$$`);
  });

  // 新增对 \textbf 和 \bf 的识别
  processedContent = processedContent.replace(/(\\textbf\{(?:[^{}]|\{[^{}]*\})+\})/g, (match) => {
    return replaceWithPlaceholder(`$${match}$`);
  });
  processedContent = processedContent.replace(/(\\bf\{(?:[^{}]|\{[^{}]*\})+\})/g, (match) => {
    return replaceWithPlaceholder(`$${match}$`);
  });

  // 2. 修复：只在隔离的数组中安全地修复命令
  for (let i = 0; i < mathExpressions.length; i++) {
    mathExpressions[i] = mathExpressions[i].replace(
      /\\color\{([^}]+)\}\{([^}]+)\}/g,
      '\\textcolor{$1}{$2}',
    );
  }

  // 3. 渲染与还原
  let rendered = md.render(processedContent); // *** 使用 markdown-it 进行解析 ***

  // 还原修复好的公式
  rendered = rendered.replace(/@@MATH_PLACEHOLDER_(\d+)@@/g, (match, index) => {
    return mathExpressions[parseInt(index, 10)];
  });

  // 清理多余的 <p> 标签
  rendered = rendered.replace(/<p>\s*(\$\$[\s\S]+?\$\$|\\\[[\s\S]+?\\\])\s*<\/p>/g, '$1');

  htmlContent.value = rendered;
  await nextTick(); // 等待DOM更新

  if (window.MathJax) {
    try {
      if (window.MathJax.typesetClear) {
        window.MathJax.typesetClear([containerRef.value]);
      }
      await window.MathJax.typesetPromise([containerRef.value]); // 异步渲染MathJax
    } catch (err) {
      console.warn('MathJax渲染失败:', err);
    }
  }
};

// 生命周期：组件挂载时渲染
onMounted(renderMarkdown);

// 监听content变化，重新渲染
watch(() => props.content, renderMarkdown);
</script>

<template>
  <!-- 错误修正：必须使用v-html来渲染HTML内容 -->
  <div ref="containerRef" class="markdown-content prose max-w-none" v-html="htmlContent"></div>
</template>

<style>
/* 样式与 shuxue.vue 保持一致，此处省略以减少代码重复。
   在实际项目中，可以考虑将这些共享样式提取到一个单独的 .css 文件中。*/
.markdown-content {
  font-family: 'LXGW WenKai', serif !important;
  overflow-wrap: break-word;
  color: var(--theme-color); /* 跟随主题 */
  background: var(--theme-bg);
  border-radius: 8px;
  padding: 0px;
  box-shadow: none;
  box-sizing: border-box;
  line-height: 1.4; /* 调整为更舒适的行高 */
  font-size: 18px !important; /* 控制基础字体大小 */
  max-width: 100%;
  /* overflow-x: hidden !important; /* 已被证明是有害的，移除 */
}
.markdown-content * {
  font-family: 'LXGW WenKai', serif !important;
  max-width: 100%;
  box-sizing: border-box;
}
.markdown-content .md-indent {
  display: inline-block;
  width: 2em;
}

/* 数学公式样式优化 */
.markdown-content :deep(.mjx-container) {
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0.25rem 0;
  margin: 0.5rem 0;
  transition: all 0.2s ease;
}

.markdown-content :deep(.mjx-container:hover) {
  background-color: rgba(0, 0, 0, 0.03);
}

/* 移动端优化 */
@media (max-width: 640px) {
  .markdown-content :deep(.mjx-container) {
    min-width: auto !important;
    width: 100% !important;
  }
}
/* 表格容器 */
.markdown-content .table-container {
  width: 100%;
  overflow-x: auto;
  margin: 1rem 0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  /* Hide scrollbar but keep functionality */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
.markdown-content .table-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, and Opera */
}

/* 通用表格样式 */
.markdown-content table {
  width: auto; /* 让表格根据内容自适应宽度 */
  max-width: 100%; /* 但不超过容器宽度 */
  border-collapse: collapse;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
  background: var(--table-bg);
  color: var(--theme-color);
  /* 移除固定最小宽度，让表格能够自适应内容 */
  min-width: auto;
  border: 1px solid var(--table-border-color);
}

.markdown-content th,
.markdown-content td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid var(--table-border-color);
  border-right: 1px solid var(--table-border-color); /* Add vertical lines back */
  vertical-align: top;
}

.markdown-content th:last-child,
.markdown-content td:last-child {
  border-right: none; /* Remove border from the last column */
}

.markdown-content th {
  background: var(--table-header-bg);
  color: var(--theme-color);
  font-weight: 600;
  border-bottom-width: 2px;
}

.markdown-content tr:last-child td {
  border-bottom: none;
}

.markdown-content td {
  background: var(--table-bg);
  color: var(--theme-color);
}

.markdown-content tr:nth-child(even) td {
  background-color: var(--table-row-alt-bg);
}

.markdown-content tr:hover td {
  background-color: var(--table-header-bg);
  transition: background-color 0.2s ease;
}

/* 响应式表格 */
@media (max-width: 768px) {
  .markdown-content {
    font-size: 15px !important; /* Smaller font for mobile */
    line-height: 1.5; /* Slightly tighter line-height for mobile */
  }

  .markdown-content h1,
  .markdown-content h2,
  .markdown-content h3 {
    margin-top: 0.8em;
    margin-bottom: 0.4em;
    line-height: 1.3;
  }

  .markdown-content p,
  .markdown-content ul,
  .markdown-content ol {
    margin-bottom: 0.6em;
  }

  .markdown-content .table-container {
    margin: 0.5rem 0;
    border-radius: 6px;
  }

  .markdown-content table {
    font-size: 0.95rem; /* Slightly larger font for mobile tables */
    min-width: auto; /* 移动端也让表格自适应内容宽度 */
  }

  .markdown-content th,
  .markdown-content td {
    padding: 6px 10px;
    white-space: normal;
    min-width: 100px;
  }

  .markdown-content th {
    font-size: 0.8rem;
    padding: 4px 6px;
  }
}

/* 错误提示样式 */
.mathjax-error {
  background-color: var(--theme-error-bg);
  border: 1px solid var(--theme-error-border);
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  color: var(--theme-error-text);
  font-size: 0.875rem;
}

/* 代码块样式优化 */
.markdown-content pre {
  background: var(--table-row-alt-bg);
  border: 1px solid #e5e5d7;
  border-radius: 6px;
  color: var(--theme-color);
  font-size: 1em;
  box-shadow: none;
  padding: 10px 14px;
  margin: 1em 0;
}

.markdown-content code {
  background: var(--table-header-bg);
  color: var(--theme-color);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 1em;
}

/* 链接样式优化 */
.markdown-content a {
  color: var(--theme-link-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-bottom-color 0.2s ease;
}

.markdown-content a:hover {
  border-bottom-color: var(--theme-link-color);
}

/* 图片样式优化 */
.markdown-content img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 1rem 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: var(--image-bg-color);
}

/* 引用块样式 */
.markdown-content blockquote {
  margin: 1em 0;
  padding: 0.8em 1.2em;
  border-left: 5px solid var(--table-border-color);
  border-radius: 0 8px 8px 0;
  background: var(--blockquote-bg);
  color: var(--theme-color);
  font-style: italic;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 桌面端优化：表格、代码块、引用等护眼简洁风格 */
@media (min-width: 641px) {
  .markdown-content .table-container {
    background: var(--table-row-alt-bg);
    box-shadow: none;
    border-radius: 6px;
    margin: 1.2rem 0;
    padding: 0.5rem 0.5rem 0.5rem 0.5rem;
  }
  .markdown-content table {
    background: var(--table-bg);
    border: 1px solid #e5e5d7;
    border-radius: 6px;
    box-shadow: none;
    min-width: auto; /* 桌面端也让表格自适应内容宽度 */
  }
  .markdown-content th {
    background: var(--table-header-bg);
    color: var(--theme-color);
    font-weight: 600;
    border-bottom: 2px solid #e5e5d7;
  }
  .markdown-content td {
    background: var(--table-bg);
    color: var(--theme-color);
  }
  .markdown-content tr:nth-child(even) td {
    background-color: var(--table-row-alt-bg);
  }
  .markdown-content tr:hover td {
    background-color: var(--table-header-bg);
    transition: background-color 0.2s ease;
  }
  .markdown-content pre {
    background: var(--theme-bg);
    border: 1px solid #e5e5d7;
    border-radius: 6px;
    color: var(--theme-color);
    font-size: 1em;
    box-shadow: none;
    padding: 14px 18px;
    margin: 1.2em 0;
  }
  .markdown-content code {
    background: var(--theme-bg);
    color: var(--theme-color);
    border-radius: 4px;
    padding: 2px 6px;
    font-size: 1em;
  }
  .markdown-content blockquote {
    background: var(--blockquote-bg);
    border-left-color: var(--table-border-color);
    padding: 1.2em 1.8em;
  }
  .markdown-content a {
    color: var(--theme-link-color);
    border-bottom: 1px dashed var(--theme-link-color);
    text-decoration: none;
    transition: border-bottom-color 0.2s;
  }
  .markdown-content a:hover {
    border-bottom-color: var(--theme-link-color);
  }
  .markdown-content img {
    border-radius: 6px;
    box-shadow: none;
    margin: 1rem 0;
    max-width: 100%;
    background: var(--image-bg-color);
  }
}

/* 移动端样式保持原样 */
</style>
