<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
    />
    <style>
      html,
      body {
        touch-action: pan-x pan-y;
      }
    </style>

    <!-- MathJax配置已移除，现在使用动态按需加载 -->
    <!-- 引入霞鹜文楷字体（保持原字体配置） -->
    <link
      href="https://cdn.jsdelivr.net/npm/lxgw-wenkai-webfont@1.6.0/style.min.css"
      rel="stylesheet"
    />

    <!-- 公式字体兼容设置 -->
    <style>
      @font-face {
        font-family: 'SimSunFallback';
        src: local('SimSun'), local('宋体'), local('Noto Serif SC'), local('Microsoft YaHei');
      }
      /* 确保 MathJax 公式使用指定字体 */
      mjx-container {
        font-family: 'SimSunFallback', 'STIXGeneral', serif !important;
      }
    </style>

    <title>Vite App</title>
  </head>
  <body>
    <div id="app"></div>
    <!-- Vue 应用入口（保持不变） -->
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
