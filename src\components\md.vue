<script setup>
import DOMPurify from 'dompurify';
import MarkdownIt from 'markdown-it';
import { nextTick, onErrorCaptured, onMounted, onUnmounted, ref, watch } from 'vue';

// 防抖函数
const debounce = (func, wait) => {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

// Props定义 - 添加输入验证
const props = defineProps({
  content: {
    type: String,
    default: '',
    validator: (value) => {
      // 限制内容长度，防止DoS攻击
      if (value.length > 100000) {
        console.warn('Content too long, truncating to 100000 characters');
        return false;
      }
      // 检查危险模式
      if (/<script|javascript:|data:|vbscript:/i.test(value)) {
        console.warn('Potentially dangerous content detected');
        return false;
      }
      return true;
    },
  },
  isPreformatted: {
    type: Boolean,
    default: false,
  },
  // 简化的MathJax配置
  mathJaxConfig: {
    type: Object,
    default: () => ({
      tex: {
        inlineMath: [
          ['$', '$'],
          ['\\(', '\\)'],
        ],
        displayMath: [
          ['$$', '$$'],
          ['\\[', '\\]'],
        ],
        processEscapes: true,
        packages: { '[+]': ['ams'] },
      },
    }),
  },
  renderOnMount: {
    type: Boolean,
    default: true,
  },
  renderOnUpdate: {
    type: Boolean,
    default: true,
  },
  // 性能优化配置
  enableCache: {
    type: Boolean,
    default: true,
  },
  debounceDelay: {
    type: Number,
    default: 300,
  },
  // DOMPurify配置
  sanitizeConfig: {
    type: Object,
    default: () => ({
      ALLOWED_TAGS: [
        'p',
        'br',
        'strong',
        'em',
        'code',
        'pre',
        'h1',
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
        'ul',
        'ol',
        'li',
        'blockquote',
        'table',
        'thead',
        'tbody',
        'tr',
        'td',
        'th',
        'a',
        'img',
        'div',
        'span',
      ],
      ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'class', 'id', 'data-*'],
      ALLOW_DATA_ATTR: false,
      FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'iframe'],
      FORBID_ATTR: ['onclick', 'onerror', 'onload', 'onmouseover', 'onfocus', 'onblur'],
      KEEP_CONTENT: true,
      SANITIZE_DOM: true,
      SANITIZE_NAMED_PROPS: true,
    }),
  },
});

const emits = defineEmits(['render-start', 'render-complete', 'render-error']);

const rawContent = ref('');
const mathContainer = ref(null);
let resizeObserver = null;

// 限制缓存大小，防止内存泄漏
const MAX_CACHE_SIZE = 50;
const renderCache = new Map();

// 初始化 Markdown 解析器 - 使用安全配置
const md = new MarkdownIt({
  html: false, // 禁用HTML标签，提高安全性
  linkify: true, // 启用自动链接，但会被DOMPurify清理
  typographer: true, // 启用排版增强
  breaks: false, // 禁用换行符转换
  quotes: '""\'\'', // 智能引号
  // 语法高亮函数 - 安全版本
  highlight: function (str, lang) {
    // 简单的代码高亮，避免XSS风险
    if (lang && /^[a-zA-Z0-9_-]+$/.test(lang)) {
      return `<pre><code class="language-${lang}">${md.utils.escapeHtml(str)}</code></pre>`;
    }
    return `<pre><code>${md.utils.escapeHtml(str)}</code></pre>`;
  },
});

// 安全的Markdown渲染函数
const renderMarkdown = (content) => {
  console.log('🔒 开始安全渲染Markdown内容...');

  if (!content) return '';

  // 输入验证
  if (content.length > 100000) {
    console.warn('⚠️ 内容过长，截断到100000字符');
    content = content.substring(0, 100000);
  }

  // 缓存检查
  const contentHash = `v1_${content.length}_${content.substring(0, 50)}`;
  if (props.enableCache && renderCache.has(contentHash)) {
    console.log('📦 使用缓存渲染');
    return renderCache.get(contentHash);
  }

  emits('render-start');

  try {
    // 第一步：使用markdown-it渲染
    let rendered = md.render(content);

    // 第二步：使用DOMPurify清理HTML
    const cleanHTML = DOMPurify.sanitize(rendered, props.sanitizeConfig);

    console.log('✅ Markdown渲染完成');
    emits('render-complete');

    // 缓存管理 - 限制大小
    if (props.enableCache) {
      if (renderCache.size >= MAX_CACHE_SIZE) {
        const firstKey = renderCache.keys().next().value;
        renderCache.delete(firstKey);
      }
      renderCache.set(contentHash, cleanHTML);
    }

    return cleanHTML;
  } catch (error) {
    console.error('❌ Markdown渲染失败:', error);
    emits('render-error', error);
    return `<p>渲染错误: ${md.utils.escapeHtml(error.message)}</p>`;
  }
};

// 渲染数学公式（MathJax 适配）- 简化版本
let mathRenderTimeout = null;
const renderMath = async () => {
  if (!mathContainer.value) return;

  await nextTick();

  // 防抖MathJax渲染
  clearTimeout(mathRenderTimeout);
  mathRenderTimeout = setTimeout(async () => {
    try {
      // 等待MathJax加载
      if (window.MathJax?.startup?.promise) {
        await window.MathJax.startup.promise;
      }

      await nextTick();

      // 检查是否有数学公式需要渲染
      const containerHTML = mathContainer.value.innerHTML;
      const hasMath =
        containerHTML.includes('$') ||
        containerHTML.includes('\\[') ||
        containerHTML.includes('\\(');

      if (!hasMath) {
        console.log('⏭️ 跳过MathJax渲染（无数学公式）');
        return;
      }

      console.log('🧮 开始MathJax渲染...');

      // 清理之前的渲染结果
      if (window.MathJax.typesetClear) {
        window.MathJax.typesetClear([mathContainer.value]);
      }

      // 重新渲染
      await window.MathJax.typesetPromise([mathContainer.value]);
      mathContainer.value.classList.add('math-rendered');

      const renderedElements = mathContainer.value.querySelectorAll('mjx-container');
      console.log('✅ MathJax渲染完成，渲染了', renderedElements.length, '个公式');
    } catch (error) {
      console.error('❌ MathJax渲染失败:', error);
      emits('render-error', error);
    }
  }, 200);
};

// 响应式重新渲染
const handleResize = debounce(() => {
  if (mathContainer.value) {
    renderMath();
  }
}, 300);

// 初始化函数 - 简化版本
const initialize = async () => {
  if (!props.content) return;

  try {
    console.log('🚀 安全Markdown组件初始化...');

    // 渲染Markdown内容
    if (props.isPreformatted) {
      rawContent.value = DOMPurify.sanitize(props.content, props.sanitizeConfig);
    } else {
      rawContent.value = renderMarkdown(props.content);
    }

    if (props.renderOnMount) {
      // 检查是否包含数学公式
      const hasMath =
        props.content.includes('$') ||
        props.content.includes('\\[') ||
        props.content.includes('\\(');

      if (hasMath) {
        console.log('📐 开始MathJax渲染...');
        await renderMath();
      } else {
        console.log('⏭️ 跳过MathJax渲染（无数学公式）');
      }
    }

    // 监听窗口大小变化
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver(handleResize);
      if (mathContainer.value) {
        resizeObserver.observe(mathContainer.value);
      }
    }
  } catch (error) {
    console.error('❌ 初始化失败:', error);
    emits('render-error', error);
  }
};

onMounted(initialize);

// 防抖渲染函数
const debouncedRender = debounce(async (newContent) => {
  try {
    if (props.isPreformatted) {
      rawContent.value = DOMPurify.sanitize(newContent, props.sanitizeConfig);
    } else {
      rawContent.value = renderMarkdown(newContent);
    }

    // 只有包含数学公式时才调用MathJax
    if (newContent.includes('$') || newContent.includes('\\[') || newContent.includes('\\(')) {
      await renderMath();
    }
  } catch (error) {
    console.error('❌ 内容更新失败:', error);
    emits('render-error', error);
  }
}, props.debounceDelay);

// 监听内容变化
watch(
  () => props.content,
  async (newContent, oldContent) => {
    if (!newContent || !props.renderOnUpdate || newContent === oldContent) return;

    const contentDiff = Math.abs(newContent.length - (oldContent?.length || 0));

    if (contentDiff > 1000) {
      // 大幅变化，立即渲染
      try {
        if (props.isPreformatted) {
          rawContent.value = DOMPurify.sanitize(newContent, props.sanitizeConfig);
        } else {
          rawContent.value = renderMarkdown(newContent);
        }

        const hasMath =
          newContent.includes('$') || newContent.includes('\\[') || newContent.includes('\\(');
        if (hasMath) {
          await renderMath();
        }
      } catch (error) {
        console.error('❌ 内容更新失败:', error);
        emits('render-error', error);
      }
    } else {
      // 小幅变化，防抖渲染
      debouncedRender(newContent);
    }
  },
  { deep: true },
);

// 清理资源
onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  if (window.MathJax && mathContainer.value) {
    window.MathJax.typesetClear([mathContainer.value]);
  }
  // 清理缓存
  renderCache.clear();
});

// 错误捕获
onErrorCaptured((err, _, info) => {
  console.error('❌ 组件错误:', err, info);
  emits('render-error', err);
  return false;
});
</script>

<template>
  <div
    ref="mathContainer"
    class="secure-markdown-content prose max-w-none tex2jax_process"
    v-html="rawContent"
  ></div>
</template>

<style scoped>
/* 安全的Markdown内容样式 */
.secure-markdown-content {
  font-family: 'LXGW WenKai', serif !important;
  overflow-wrap: break-word;
  color: var(--theme-color);
  background: var(--theme-bg);
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  line-height: 1.6;
  font-size: 18px !important;
  max-width: 100%;
}

.secure-markdown-content * {
  font-family: 'LXGW WenKai', serif !important;
  max-width: 100%;
  box-sizing: border-box;
}

/* 数学公式样式优化 */
.secure-markdown-content :deep(.mjx-container) {
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0.25rem 0;
  margin: 0.5rem 0;
  transition: all 0.2s ease;
  border-radius: 4px;
}

.secure-markdown-content :deep(.mjx-container:hover) {
  background-color: rgba(0, 0, 0, 0.03);
}

/* 表格样式 - 简化版本 */
.secure-markdown-content table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border-radius: 8px;
  background: var(--table-bg);
  color: var(--theme-color);
  border: 1px solid var(--table-border-color);
  margin: 1rem 0;
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

.secure-markdown-content thead,
.secure-markdown-content tbody,
.secure-markdown-content tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

.secure-markdown-content th,
.secure-markdown-content td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid var(--table-border-color);
  border-right: 1px solid var(--table-border-color);
  vertical-align: top;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.secure-markdown-content th:last-child,
.secure-markdown-content td:last-child {
  border-right: none;
}

.secure-markdown-content th {
  background: var(--table-header-bg);
  color: var(--theme-color);
  font-weight: 600;
  border-bottom-width: 2px;
}

.secure-markdown-content tr:last-child td {
  border-bottom: none;
}

.secure-markdown-content tr:nth-child(even) td {
  background-color: var(--table-row-alt-bg);
}

.secure-markdown-content tr:hover td {
  background-color: var(--table-header-bg);
  transition: background-color 0.2s ease;
}

/* 代码块样式 */
.secure-markdown-content pre {
  background: var(--table-row-alt-bg);
  border: 1px solid var(--table-border-color);
  border-radius: 6px;
  color: var(--theme-color);
  font-size: 0.9em;
  padding: 12px 16px;
  margin: 1em 0;
  overflow-x: auto;
}

.secure-markdown-content code {
  background: var(--table-header-bg);
  color: var(--theme-color);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.9em;
}

.secure-markdown-content pre code {
  background: transparent;
  padding: 0;
}

/* 链接样式 */
.secure-markdown-content a {
  color: var(--theme-link-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-bottom-color 0.2s ease;
}

.secure-markdown-content a:hover {
  border-bottom-color: var(--theme-link-color);
}

/* 图片样式 */
.secure-markdown-content img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 1rem 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 引用块样式 */
.secure-markdown-content blockquote {
  margin: 1em 0;
  padding: 0.8em 1.2em;
  border-left: 4px solid var(--theme-link-color);
  border-radius: 0 6px 6px 0;
  background: var(--blockquote-bg);
  color: var(--theme-color);
  font-style: italic;
}

/* 列表样式 */
.secure-markdown-content ul,
.secure-markdown-content ol {
  padding-left: 2em;
  margin: 1em 0;
}

.secure-markdown-content li {
  margin: 0.5em 0;
}

/* 标题样式 */
.secure-markdown-content h1,
.secure-markdown-content h2,
.secure-markdown-content h3,
.secure-markdown-content h4,
.secure-markdown-content h5,
.secure-markdown-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  line-height: 1.3;
}

.secure-markdown-content h1 {
  font-size: 2em;
  border-bottom: 2px solid var(--table-border-color);
  padding-bottom: 0.3em;
}

.secure-markdown-content h2 {
  font-size: 1.5em;
  border-bottom: 1px solid var(--table-border-color);
  padding-bottom: 0.3em;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .secure-markdown-content {
    font-size: 16px !important;
    padding: 12px;
  }

  .secure-markdown-content table {
    font-size: 14px;
  }

  .secure-markdown-content th,
  .secure-markdown-content td {
    padding: 6px 8px;
  }
}

/* 错误提示样式 */
.secure-markdown-content .error-message {
  background-color: #fee;
  border: 1px solid #fcc;
  border-radius: 6px;
  padding: 12px;
  margin: 8px 0;
  color: #c33;
  font-size: 0.9em;
}

/* 加载状态样式 */
.secure-markdown-content.loading {
  opacity: 0.7;
  pointer-events: none;
}

.secure-markdown-content.loading::after {
  content: '渲染中...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  color: #666;
}
</style>
